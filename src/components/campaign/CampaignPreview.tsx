"use client";

import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import {
  DollarSign,
  Dot,
  MoreHorizontal,
  Twitter,
  Glasses,
  Send,
} from "lucide-react";
import Image from "next/image";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Button } from "../ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";

interface FormData {
  campaignName: string;
  description: string;
  title: string;
  budget: number;
  socialAccounts: {
    instagram: boolean;
    twitter: boolean;
    youtube: boolean;
    tiktok: boolean;
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

interface CampaignPreviewProps {
  formData: FormData;
  currentStep: number;
}

export default function CampaignPreview({
  formData,
  currentStep,
}: CampaignPreviewProps) {
  const renderStepOnePreview = () => (
    <Card className="p-6 bg-background border-primary">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg md:text-2xl font-semibold">
            Campaign Preview
          </h3>
        </div>

        <div className="relative rounded-lg overflow-hidden aspect-video">
          <Image
            src="https://assets.lummi.ai/assets/QmZLrB2BticWVsQ3KE1YrRT6naEmZWdJq1vnycUm6xwvZm?auto=format&w=1500"
            alt="Campaign Preview"
            width={600}
            height={300}
            className="object-cover absolute inset-0 size-full"
          />
          <Badge className="absolute top-3 left-3">
            <Dot size={30} /> Live Preview
          </Badge>
        </div>
        <p className="text-2xl font-bold">Joyner Lucas Audio Campaign</p>
        <div className="flex gap-2">
          <Badge>@joynermusic</Badge>
          <Badge>Music</Badge>
          <Badge>$1000</Badge>
          <Badge>$1 prize</Badge>
        </div>

        <p className="text-sm text-muted-foreground">
          This is a dedicated audio post for Joyner Lucas&apos;s new track
          &quot;White Noise&quot;. You just need to play the song on TikTok. You
          must use the audio and include the song in your video with the proper
          audio that will be accepted.
        </p>
      </div>
    </Card>
  );

  const renderStepTwoPreview = () => (
    <div className="space-y-4">
      <Card className="p-6 bg-card border">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">
              Minimum Follower Count per page
            </h3>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">50000+</div>
            <div className="text-sm text-muted-foreground">500000+</div>
          </div>
        </div>
      </Card>

      <Card className="p-6 bg-card border">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Minimum Views for payout</h3>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">No Minimum</div>
            <div className="text-sm text-muted-foreground">2 Million</div>
          </div>
          <Slider
            value={[formData.minViews || 750000]}
            max={2000000}
            min={0}
            step={50000}
            className="w-full"
            disabled
          />
        </div>
      </Card>

      <Card className="p-6 bg-card border">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Max Posts Per Page</h3>
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">No Limit</div>
          </div>
        </div>
      </Card>

      <Card className="p-6 bg-card border">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Max Payout Per Creator</h3>
          <div className="flex items-center justify-center">
            <DollarSign className="w-5 h-5 text-primary mr-1" />
            <span className="text-2xl font-bold text-primary">30%</span>
          </div>
        </div>
      </Card>

      <Card className="p-6 bg-card border">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Who can Join?</h3>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm">
                Creators apply, you approve/reject them.
              </span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
              <span className="text-sm">
                Anyone that meets your requirements can join!
              </span>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );

  const renderStepThreePreview = () => (
    <Card className="p-6 bg-card border">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Campaign Preview</h3>
          <Badge className="bg-green-500 text-white">Live Preview</Badge>
        </div>

        <div className="relative rounded-lg overflow-hidden bg-gradient-to-br from-orange-500 to-red-600 aspect-video">
          <Image
            src="https://image.lexica.art/full_webp/24fd65c2-8b62-45f4-b1b5-22403fbcc83f"
            alt="Campaign Preview"
            width={600}
            height={300}
            className="object-cover absolute inset-0 size-full"
          />
        </div>

        {/* <div className="bg-card border rounded-lg p-4">
          <div className="flex justify-between items-center">
            <span className="text-sm">Pool</span>
            <span className="font-semibold">$10,000</span>
          </div>
          <div className="mt-2">
            <Slider
              value={[6000]}
              max={10000}
              min={0}
              className="w-full"
              disabled
            />
          </div>
          <div className="text-center text-xs text-muted-foreground mt-1">
            $6000 paid out so far
          </div>
        </div> */}

        <div>
          <div className="flex justify-between items-center mb-1">
            <h3 className="font-bold text-lg md:text-xl tracking-tight text-card-foreground">
              X Campaign
            </h3>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 rounded-full hover:bg-accent hover:text-accent-foreground"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Share</DropdownMenuItem>
                <DropdownMenuItem>Report</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <div className="flex text-sm items-center gap-2 mb-3">
            <div className="font-semibold text-muted-foreground">
              Created by
            </div>
            <div className="flex items-center gap-0.5">
              <Avatar className="size-5">
                <AvatarImage
                  src={
                    "https://xvatar.vercel.app/api/avatar/xxy.svg?rounded=40&size=80"
                  }
                  alt="Creator"
                />
                <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                  JS
                </AvatarFallback>
              </Avatar>
              <span className="font-medium">JackSparrow</span>
            </div>
          </div>

          <div className="flex text-sm justify-between items-center mb-4">
            <div className="font-semibold text-muted-foreground">
              Rate per 1000 Views
            </div>
            <Badge
              variant={"outline"}
              className={"font-bold text-base border-0"}
            >
              $800
            </Badge>
          </div>

          <div className="h-px -translate-y-2 w-full bg-muted-foreground/40" />

          <div className="flex text-sm justify-between items-center mb-6">
            <div className="font-semibold text-muted-foreground">
              Accepted Platforms
            </div>
            <div className="flex gap-1.5">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Twitter className="p-1 bg-blue-500 rounded-2xl" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Twitter</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Glasses className="p-1 bg-indigo-500 rounded-2xl" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Discord</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Send className="p-1 bg-cyan-400 rounded-2xl" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Telegram</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>

          <div className="flex relative flex-col p-8 rounded-xl z-60 border-2 backdrop-blur-lg bg-white/5 shadow-2xl border-muted-foreground/30 shadow-black/30">
            <div className="flex text-white font-bold text-base justify-between items-center mb-3">
              <div>Pool</div>
              <div>$10,000</div>
            </div>

            <div className="relative mb-4">
              <Slider
                defaultValue={[60]}
                max={100}
                step={1}
                disabled
                className="mb-1"
                trackClassName="bg-muted-foreground/50"
                rangeClassName="bg-primary"
                thumbClassName="bg-primary bg-muted"
              ></Slider>
              <div className="flex justify-between text-xs text-white mt-1">
                <span>0%</span>
                <span>100%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );

  const renderStepFourPreview = () => (
    <Card className="p-6 bg-card border">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Campaign Preview</h3>
          <Badge className="bg-green-500 text-white">Live Preview</Badge>
        </div>

        <div className="relative rounded-lg overflow-hidden bg-gradient-to-br from-orange-500 to-red-600 aspect-video">
          <Image
            src="https://image.lexica.art/full_webp/24fd65c2-8b62-45f4-b1b5-22403fbcc83f"
            alt="Campaign Preview"
            width={600}
            height={300}
            className="object-cover absolute inset-0 size-full"
          />
        </div>

        <div>
          <div className="flex justify-between items-center mb-1">
            <h3 className="font-bold text-lg md:text-xl tracking-tight text-card-foreground">
              X Campaign
            </h3>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 rounded-full hover:bg-accent hover:text-accent-foreground"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>Share</DropdownMenuItem>
                <DropdownMenuItem>Report</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <div className="flex text-sm items-center gap-2 mb-3">
            <div className="font-semibold text-muted-foreground">
              Created by
            </div>
            <div className="flex items-center gap-0.5">
              <Avatar className="size-5">
                <AvatarImage
                  src={
                    "https://xvatar.vercel.app/api/avatar/xxy.svg?rounded=40&size=80"
                  }
                  alt="Creator"
                />
                <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                  JS
                </AvatarFallback>
              </Avatar>
              <span className="font-medium">JackSparrow</span>
            </div>
          </div>

          <div className="flex text-sm justify-between items-center mb-4">
            <div className="font-semibold text-muted-foreground">
              Rate per 1000 Views
            </div>
            <Badge
              variant={"outline"}
              className={"font-bold text-base border-0"}
            >
              $800
            </Badge>
          </div>

          <div className="h-px -translate-y-2 w-full bg-muted-foreground/40" />

          <div className="flex text-sm justify-between items-center mb-6">
            <div className="font-semibold text-muted-foreground">
              Accepted Platforms
            </div>
            <div className="flex gap-1.5">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Twitter className="p-1 bg-blue-500 rounded-2xl" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Twitter</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Glasses className="p-1 bg-indigo-500 rounded-2xl" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Discord</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Send className="p-1 bg-cyan-400 rounded-2xl" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Telegram</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>

          <div className="flex relative flex-col p-8 rounded-xl z-60 border-2 backdrop-blur-lg bg-white/5 shadow-2xl border-muted-foreground/30 shadow-black/30">
            <div className="flex text-white font-bold text-base justify-between items-center mb-3">
              <div>Pool</div>
              <div>$10,000</div>
            </div>

            <div className="relative mb-4">
              <Slider
                defaultValue={[60]}
                max={100}
                step={1}
                disabled
                className="mb-1"
                trackClassName="bg-muted-foreground/50"
                rangeClassName="bg-primary"
                thumbClassName="bg-primary bg-muted"
              ></Slider>
              <div className="flex justify-between text-xs text-white mt-1">
                <span>0%</span>
                <span>100%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );

  return (
    <div className="space-y-6">
      {currentStep === 1 && renderStepOnePreview()}
      {currentStep === 2 && renderStepTwoPreview()}
      {currentStep === 3 && renderStepThreePreview()}
      {currentStep === 4 && renderStepFourPreview()}
    </div>
  );
}
