"use client";

import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";

interface FormData {
  budget: number;
  costPerView: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

interface StepThreeProps {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
}

export default function StepThree({
  formData,
  updateFormData,
}: StepThreeProps) {
  const targetViews = 750000000; // 750M views
  const platformFees = formData.budget * 0.2; // 20% platform fees
  const totalPay = formData.budget - platformFees;

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-6">Payment Overview</h2>
      </div>

      {/* Budget Breakdown */}
      <div className="space-y-4">
        <div className="flex justify-between items-center py-2 border-b border-border">
          <span className="text-base">Total Budget</span>
          <span className="text-lg font-semibold text-primary">
            ${formData.budget.toLocaleString()}
          </span>
        </div>

        <div className="flex justify-between items-center py-2 border-b border-border">
          <span className="text-base">Target Views</span>
          <span className="text-lg font-semibold">
            {targetViews.toLocaleString()}
          </span>
        </div>

        <div className="flex justify-between items-center py-2 border-b border-border">
          <span className="text-base">Platform Fees</span>
          <span className="text-lg font-semibold">20%</span>
        </div>

        <div className="flex justify-between items-center py-2 border-b border-border">
          <span className="text-base">Cost Per View</span>
          <span className="text-lg font-semibold text-primary">
            ${formData.costPerView.toFixed(2)}
          </span>
        </div>
      </div>

      {/* Total Pay Section */}
      <div className="mt-8">
        <Card className="bg-primary/10 border-primary/20 p-6">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-lg font-semibold">Total Pay</h3>
              <p className="text-sm text-muted-foreground">
                Amount paid to creators
              </p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-primary">
                ${totalPay.toLocaleString()}
              </div>
              <Button
                className="mt-2 bg-primary hover:bg-primary/90 text-primary-foreground"
                size="sm"
              >
                Continue Wallet
              </Button>
            </div>
          </div>
        </Card>
      </div>

      {/* Budget Adjustment */}
      <div className="space-y-4">
        <Label className="text-base font-medium">Adjust Campaign Budget</Label>
        <div className="space-y-4">
          <div className="flex justify-between text-sm">
            <span>$100</span>
            <span>$10,000</span>
          </div>
          <Slider
            value={[formData.budget]}
            onValueChange={(value) => updateFormData({ budget: value[0] })}
            max={10000}
            min={100}
            step={100}
            className="w-full"
          />
          <div className="text-center text-lg font-semibold text-primary">
            ${formData.budget.toLocaleString()}
          </div>
        </div>
      </div>

      {/* Estimated Views */}
      <div className="bg-card border rounded-lg p-4">
        <div className="text-center">
          <p className="text-sm text-muted-foreground mb-1">Estimated Views</p>
          <p className="text-xl font-semibold">4.7M</p>
        </div>
      </div>
    </div>
  );
}
