"use client";

import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { X, Globe, Instagram } from "lucide-react";

interface FormData {
  campaignName: string;
  description: string;
  links: {
    twitter: string;
    instagram: string;
    website: string;
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

interface StepOneProps {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
}

export default function StepOne({ formData, updateFormData }: StepOneProps) {
  const handleInputChange = (field: string, value: string) => {
    updateFormData({ [field]: value });
  };

  const handleLinkChange = (platform: string, value: string) => {
    updateFormData({
      links: {
        ...formData.links,
        [platform]: value,
      },
    });
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold mb-6">Launch A Campaign</h1>
      <div className="space-y-4">
        <Label className="text-base font-medium">Link your socials</Label>

        <div className="space-y-3 mt-2">
          {/* Twitter */}
          <div className="flex items-center gap-3 relative">
            <X className="w-4 h-4 text-primary absolute left-2 top-1/2 -translate-y-1/2" />
            <Input
              placeholder="https://x.com/..."
              value={formData.links.twitter}
              onChange={(e) => handleLinkChange("twitter", e.target.value)}
              className="flex-1 bg-background dark:bg-input/30 pl-8"
            />
          </div>

          {/* Instagram */}
          <div className="flex items-center gap-3 relative">
            <Instagram className="w-4 h-4 text-primary absolute left-2 top-1/2 -translate-y-1/2" />
            <Input
              placeholder="https://instagram.com/..."
              value={formData.links.instagram}
              onChange={(e) => handleLinkChange("instagram", e.target.value)}
              className="flex-1 bg-background dark:bg-input/30 pl-8"
            />
          </div>

          {/* Website */}
          <div className="flex items-center gap-3 relative">
            <Globe className="w-4 h-4 text-primary absolute left-2 top-1/2 -translate-y-1/2" />
            <Input
              placeholder="https://website.com/"
              value={formData.links.website}
              onChange={(e) => handleLinkChange("website", e.target.value)}
              className="flex-1 bg-background dark:bg-input/30 pl-8"
            />
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <Label className="text-base font-medium flex items-center gap-2">
          Description
          <div className="w-4 h-4 rounded-full bg-primary flex items-center justify-center">
            <span className="text-xs text-black font-bold">!</span>
          </div>
        </Label>
        <Textarea
          placeholder={`Lorem ipsum dolor sit amet consectetur adipiscing elit placerat nec ut volutpat, dignissim et rhoncus urna.

Lorem ipsum dolor sit amet consectetur, adipisicing elit. Pariatur, doloribus.`}
          value={formData.description}
          onChange={(e) => handleInputChange("description", e.target.value)}
          className="min-h-[120px] bg-background dark:bg-input/30 resize-none"
        />
      </div>
    </div>
  );
}
