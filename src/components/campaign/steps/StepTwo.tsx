"use client";

import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { HelpCircle, Trash2 } from "lucide-react";

interface FormData {
  campaignType: string;
  geoRestrictions: string;
  pageTypes: string[];
  targetAudience: string;
  hashtags: string;
  minFollowers: number;
  minViews: number;
  maxPosts: number;
  maxPayout: number;
  requirements: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

interface StepTwoProps {
  formData: FormData;
  updateFormData: (data: Partial<FormData>) => void;
}

export default function StepTwo({ formData, updateFormData }: StepTwoProps) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleInputChange = (field: string, value: any) => {
    updateFormData({ [field]: value });
  };

  const handlePageTypeToggle = (pageType: string) => {
    const currentTypes = formData.pageTypes || [];
    const newTypes = currentTypes.includes(pageType)
      ? currentTypes.filter((type) => type !== pageType)
      : [...currentTypes, pageType];
    updateFormData({ pageTypes: newTypes });
  };

  return (
    <div className="space-y-6">
      {/* Campaign Type */}
      <div className="space-y-3">
        <Label className="text-base font-medium">
          Campaign Type (Optional)
        </Label>
        <RadioGroup
          value={formData.campaignType}
          onValueChange={(value) => handleInputChange("campaignType", value)}
          className="space-y-2"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="General" id="general" />
            <Label htmlFor="general">General</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="Sponsored Content" id="sponsored" />
            <Label htmlFor="sponsored">Sponsored Content</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="Other" id="other" />
            <Label htmlFor="other">Other</Label>
          </div>
        </RadioGroup>
      </div>

      {/* Geographic Restrictions */}
      <div className="space-y-3">
        <Label className="text-base font-medium">Geographic Restrictions</Label>
        <RadioGroup
          value={formData.geoRestrictions}
          onValueChange={(value) => handleInputChange("geoRestrictions", value)}
          className="space-y-2"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="Top 5 Countries only" id="top5" />
            <Label htmlFor="top5">Top 5 Countries only</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="Global" id="global" />
            <Label htmlFor="global">Global</Label>
          </div>
        </RadioGroup>
        <p className="text-sm text-muted-foreground">
          Will be your top 5 countries
        </p>
      </div>

      {/* Page Types */}
      <div className="space-y-3">
        <Label className="text-base font-medium">
          What types of pages are allowed to Join this campaign?
        </Label>
        <p className="text-sm text-muted-foreground">Select all that apply</p>

        <div className="space-y-3">
          {[
            "Fan pages (artist pages, Celebrity fan pages, Hate memes)",
            "Meme pages (funny memes, viral memes)",
            "Video Clipping channels (Joe, YouTube, sports highlights)",
            "Podcast channels",
            "Other",
          ].map((pageType) => (
            <div key={pageType} className="flex items-center space-x-2">
              <Checkbox
                id={pageType}
                checked={formData.pageTypes?.includes(pageType)}
                onCheckedChange={() => handlePageTypeToggle(pageType)}
              />
              <Label htmlFor={pageType} className="text-sm">
                {pageType}
              </Label>
            </div>
          ))}
        </div>
        <p className="text-sm text-muted-foreground">
          What are your specific page types?
        </p>
      </div>

      {/* Target Creator Audience Location */}
      <div className="space-y-3">
        <Label className="text-base font-medium flex items-center gap-2">
          Target Creator Audience Location
          <HelpCircle className="w-4 h-4 text-muted-foreground" />
        </Label>
        <RadioGroup
          value={formData.targetAudience}
          onValueChange={(value) => handleInputChange("targetAudience", value)}
          className="space-y-2"
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="Top 5 Countries" id="target-top5" />
            <Label htmlFor="target-top5">Top 5 Countries</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="Top 3 Countries" id="target-top3" />
            <Label htmlFor="target-top3">Top 3 Countries</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="Top 8 Countries" id="target-top8" />
            <Label htmlFor="target-top8">Top 8 Countries</Label>
          </div>
        </RadioGroup>
        <p className="text-sm text-muted-foreground">
          USA, Canada, UK, Australia, Germany, France, Italy, Spain, Netherlands
        </p>
      </div>

      {/* Hashtags */}
      <div className="space-y-2">
        <Label className="text-base font-medium">Hashtags (Optional)</Label>
        <Input
          placeholder="Write hashtags separated by commas"
          value={formData.hashtags}
          onChange={(e) => handleInputChange("hashtags", e.target.value)}
          className="bg-background dark:bg-input/30"
        />
      </div>

      {/* Minimum Follower Count per page */}
      <div className="space-y-3">
        <Label className="text-base font-medium">
          Minimum Follower Count per page
        </Label>
        <div className="space-y-2">
          <RadioGroup
            value={formData.minFollowers.toString()}
            onValueChange={(value) =>
              handleInputChange("minFollowers", parseInt(value))
            }
            className="space-y-2"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="1000" id="1k" />
              <Label htmlFor="1k">No Limit</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="50000" id="50k" />
              <Label htmlFor="50k">50000+</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="100000" id="100k" />
              <Label htmlFor="100k">100000+</Label>
            </div>
          </RadioGroup>
        </div>
      </div>

      {/* Minimum Views for payout */}
      <div className="space-y-3">
        <Label className="text-base font-medium">
          Minimum Views for payout
        </Label>
        <div className="space-y-4">
          <div className="flex justify-between text-sm">
            <span>No Minimum</span>
            <span>2 Million</span>
          </div>
          <Slider
            value={[formData.minViews]}
            onValueChange={(value) => handleInputChange("minViews", value[0])}
            max={2000000}
            min={0}
            step={50000}
            className="w-full"
          />
          <div className="text-center text-sm text-muted-foreground">
            {formData.minViews.toLocaleString()}
          </div>
        </div>
      </div>

      {/* Max Posts Per Page */}
      <div className="space-y-3">
        <Label className="text-base font-medium">Max Posts Per Page</Label>
        <div className="space-y-4">
          <div className="flex justify-between text-sm">
            <span>No Limit</span>
          </div>
          <Slider
            value={[formData.maxPosts]}
            onValueChange={(value) => handleInputChange("maxPosts", value[0])}
            max={10}
            min={0}
            step={1}
            className="w-full"
          />
          <div className="text-center text-sm text-muted-foreground">
            {formData.maxPosts === 0 ? "No Limit" : formData.maxPosts}
          </div>
        </div>
      </div>

      {/* Max Payout Per Creator */}
      <div className="space-y-3">
        <Label className="text-base font-medium flex items-center gap-2">
          Max Payout Per Creator
          <HelpCircle className="w-4 h-4 text-muted-foreground" />
        </Label>
        <div className="space-y-4">
          <div className="flex justify-between text-sm">
            <span>30%</span>
          </div>
          <Slider
            value={[formData.maxPayout]}
            onValueChange={(value) => handleInputChange("maxPayout", value[0])}
            max={100}
            min={10}
            step={5}
            className="w-full"
          />
          <div className="text-center text-sm text-muted-foreground">
            {formData.maxPayout}%
          </div>
        </div>
      </div>

      {/* Who can Join */}
      <div className="space-y-3">
        <Label className="text-base font-medium">Who can Join?</Label>
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox id="creators-apply" defaultChecked />
            <Label htmlFor="creators-apply" className="text-sm">
              Creators apply, you approve/reject them.
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox id="anyone-join" />
            <Label htmlFor="anyone-join" className="text-sm">
              Anyone that meets your requirements can join!
            </Label>
          </div>
        </div>
      </div>

      {/* Asset links */}
      <div className="space-y-2">
        <Label className="text-base font-medium flex items-center gap-2">
          Asset links
          <HelpCircle className="w-4 h-4 text-muted-foreground" />
        </Label>
        <Input
          placeholder="https://drive.google.com/drive/folders/..."
          className="bg-background dark:bg-input/30"
        />
      </div>

      {/* Requirements */}
      <div className="space-y-2">
        <Label className="text-base font-medium">Requirements</Label>
        <Textarea
          placeholder="Write any other requirements"
          value={formData.requirements}
          onChange={(e) => handleInputChange("requirements", e.target.value)}
          className="min-h-[100px] bg-background dark:bg-input/30 resize-none"
        />
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="h-8">
              <HelpCircle className="w-4 h-4 mr-1" />
              Must Read comment
            </Button>
            <Button variant="outline" size="sm" className="h-8">
              <span className="w-4 h-4 mr-1 rounded-full bg-purple-500 flex items-center justify-center text-xs text-white">
                i
              </span>
              https://www.tiktok.com/discover/hashtag
            </Button>
            <Button variant="outline" size="sm" className="h-8">
              <span className="w-4 h-4 mr-1 rounded-full bg-red-500 flex items-center justify-center text-xs text-white">
                T
              </span>
              https://www.tiktok.com/discover/hashtag
            </Button>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="h-8">
              Must not be longer than 5 seconds
            </Button>
            <Button variant="ghost" size="sm" className="h-8 text-red-500">
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="h-8">
            Must Include Watermark
          </Button>
          <Button variant="ghost" size="sm" className="h-8 text-red-500">
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
